<?php

namespace App\Services;

use App\Models\Merchant;
use App\Models\MerchantToken;
use App\Models\O2oErrandOrder;
use App\Models\User;
use App\Models\Site;
use App\Models\Pricing;
use App\Services\UserService;
use App\Services\MapService;
use App\Services\CommonService;
use App\Services\O2oErrandOrderService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class HaiboService
{
    protected string $appKey;
    protected string $appSecret;

    // 海博订单来源Code
    const TRADE_ORDER_SOURCE_JINGDONG = 1;      // 京东
    const TRADE_ORDER_SOURCE_MEITUAN = 2;       // 美团
    const TRADE_ORDER_SOURCE_ELEME = 3;         // 饿了么
    const TRADE_ORDER_SOURCE_HAIBO = 4;         // 海博自营

    // 海博商品经营品类Code
    const CATEGORY_FOOD = 1;                    // 餐饮美食
    const CATEGORY_FRESH = 2;                   // 生鲜果蔬
    const CATEGORY_MEDICINE = 3;                // 医药健康
    const CATEGORY_SUPERMARKET = 4;             // 超市百货
    const CATEGORY_FLOWER = 5;                  // 鲜花绿植
    const CATEGORY_CAKE = 6;                    // 烘焙蛋糕
    const CATEGORY_DRINK = 7;                   // 饮品奶茶
    const CATEGORY_OTHER = 99;                  // 其他

    // 海博接口返回结果Code
    const RESULT_SUCCESS = 0;                   // 成功
    const RESULT_PARAM_ERROR = 1001;            // 参数错误
    const RESULT_NO_CAPACITY = 1002;            // 无运力
    const RESULT_DISTANCE_EXCEED = 1003;        // 距离超限
    const RESULT_SYSTEM_ERROR = 9999;           // 系统错误

    public function __construct()
    {
        // 海博平台的配置信息 - 这些需要根据实际的海博平台配置进行调整
        $this->appKey = "haibo_app_key";
        $this->appSecret = "haibo_app_secret";
    }

    /**
     * 创建或修改配送商门店
     *
     * @param array $data 门店数据
     * @return array 处理结果
     * @throws \Exception
     */
    public function createOrUpdateStore(array $data): array
    {
        Log::channel('haibo')->info('海博创建/修改配送商门店请求', $data);

        try {

            // 开始数据库事务
            DB::beginTransaction();

            // 检查是否已存在该门店
            $existingMerchant = $this->findExistingMerchant($data);

            if ($existingMerchant) {
                // 更新现有门店
                $result = $this->updateExistingStore($existingMerchant, $data);
            } else {
                // 创建新门店
                $result = $this->createNewStore($data);
            }

            DB::commit();

            Log::channel('haibo')->info('海博门店操作成功', [
                'operation' => $existingMerchant ? 'update' : 'create',
                'merchant_id' => $result['merchant_id'],
                'user_id' => $result['user_id']
            ]);

            return [
                'success' => true,
                'message' => $existingMerchant ? '门店更新成功' : '门店创建成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::channel('haibo')->error('海博门店操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            throw $e;
        }
    }


    /**
     * 查找已存在的商家
     *
     * @param array $data
     * @return Merchant|null
     */
    private function findExistingMerchant(array $data): ?Merchant
    {
        return Merchant::where('merchant_type', 'haibo')
            ->where('phone', $data['contactPhone'])
            ->first();
    }

    /**
     * 创建新门店
     *
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function createNewStore(array $data): array
    {
        // 检查该手机号是否已有用户账号
        $user = User::where('phone', $data['contactPhone'])->first();

        // 如果没有用户账号，则创建一个
        if (!$user) {
            $userService = app(UserService::class);
            $user = $userService->registerUser(
                $data['contactPhone'],
                Hash::make('123456'), // 默认密码
                '', // 空邀请码
                \App\Models\SystemConfig::PlatformPT // 平台标识为跑腿平台
            );

            Log::channel('haibo')->info("为海博商家创建关联用户账号成功", [
                'user_id' => $user->id,
                'phone' => $data['contactPhone']
            ]);
        }

        // 解析地址信息（从shopAddress中提取省市区信息）
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 创建商家账户
        $merchant = Merchant::create([
            'shop_name' => $data['shopName'],
            'phone' => $data['contactPhone'],
            'password' => Hash::make('123456'),
            'province' => $addressInfo['province'],
            'city' => $addressInfo['city'],
            'district' => $addressInfo['district'],
            'city_code' => $addressInfo['city_code'],
            'address' => $data['shopAddress'],
            'contact_name' => '',
            'email' => '',
            'merchant_type' => 'haibo',
            'status' => 1, // 海博商家默认审核通过
            'balance' => 0,
            'user_id' => $user->id,
        ]);

        // 生成配送商门店ID
        $carrierShopId = 'HB_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'create'
        ];
    }

    /**
     * 更新现有门店
     *
     * @param Merchant $merchant
     * @param array $data
     * @return array
     */
    private function updateExistingStore(Merchant $merchant, array $data): array
    {
        // 解析地址信息
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 更新商家信息
        $merchant->update([
            'shop_name' => $data['shopName'],
            'province' => $addressInfo['province'],
            'city' => $addressInfo['city'],
            'district' => $addressInfo['district'],
            'city_code' => $addressInfo['city_code'],
            'address' => $data['shopAddress'],
        ]);


        // 生成配送商门店ID
        $carrierShopId = 'HB_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $merchant->user_id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'update'
        ];
    }


    /**
     * 解析地址信息，从详细地址中提取省市区信息
     *
     * @param string $address
     * @return array
     */
    private function parseAddress(string $address): array
    {
        // 简单的地址解析逻辑，实际项目中可能需要更复杂的解析
        // 这里提供一个基础的解析示例

        $province = '';
        $city = '';
        $district = '';
        $cityCode = '';

        // 常见的省份匹配
        if (preg_match('/(北京|天津|上海|重庆)/', $address, $matches)) {
            $province = $matches[1] . '市';
            $city = $matches[1] . '市';
            $cityCode = $this->getCityCode($province);
        } elseif (preg_match('/(.+?省)(.+?市)(.+?[区县])/', $address, $matches)) {
            $province = $matches[1];
            $city = $matches[2];
            $district = $matches[3];
            $cityCode = $this->getCityCode($city);
        } elseif (preg_match('/(.+?市)(.+?[区县])/', $address, $matches)) {
            // 处理直辖市的情况
            $city = $matches[1];
            $district = $matches[2];
            if (in_array(substr($city, 0, 2), ['北京', '天津', '上海', '重庆'])) {
                $province = $city;
            }
            $cityCode = $this->getCityCode($city);
        }

        // 如果解析失败，使用默认值
        if (empty($province)) {
            $province = '浙江省';
            $city = '杭州市';
            $district = '余杭区';
            $cityCode = '330100';
        }

        return [
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'city_code' => $cityCode
        ];
    }

    /**
     * 获取城市编码（简化版本）
     *
     * @param string $cityName
     * @return string
     */
    private function getCityCode(string $cityName): string
    {
        $cityCodes = [
            '北京市' => '110100',
            '天津市' => '120100',
            '上海市' => '310100',
            '重庆市' => '500100',
            '杭州市' => '330100',
            '广州市' => '440100',
            '深圳市' => '440300',
            '成都市' => '510100',
            '武汉市' => '420100',
            '西安市' => '610100',
        ];

        return $cityCodes[$cityName] ?? '330100'; // 默认杭州
    }
    

    /**
     * 询价接口 - 按照 maiyatian 模式实现，支持已有订单查询
     *
     * @param array $data 询价请求数据
     * @return array 询价结果
     * @throws \Exception
     */
    public function valuatingWithOrderLookup(array $data): array
    {
        Log::channel('haibo')->info('海博询价请求（新模式）', $data);

        try {
            // 1. 查找用户ID
            $userId = $this->findUserByCarrierMerchantId($data['carrierMerchantId']);
            if (!$userId) {
                return [
                    'code' => self::RESULT_PARAM_ERROR,
                    'message' => '配送商ID不存在',
                    'data' => null
                ];
            }

            // 2. 查找是否已存在订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_HB)
                ->first();

            // 3. 如果订单已存在，返回订单信息
            if ($order) {
                $result = $this->formatExistingOrderResult($order);

                Log::channel('haibo')->info('海博询价成功（已有订单）', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'result' => $result
                ]);

                return [
                    'code' => self::RESULT_SUCCESS,
                    'message' => '成功',
                    'data' => $result
                ];
            }

            // 4. 如果订单不存在，使用 O2oErrandOrderService 进行询价
            $result = $this->performNewOrderValuating($userId, $data);

            Log::channel('haibo')->info('海博询价成功（新订单）', [
                'user_id' => $userId,
                'result' => $result
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博询价失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 验证询价参数
     *
     * @param array $data
     * @throws \Exception
     */
    private function validateValuatingParams(array $data): void
    {
        $requiredFields = [
            'tradeOrderSource', 'orderId', 'serviceCode', 'recipientName', 'recipientPhone',
            'recipientAddress', 'recipientLng', 'recipientLat', 'prebook', 'totalWeight',
            'carrierMerchantId', 'senderLng', 'senderLat', 'senderName', 'senderContract',
            'senderAddressDetail'
        ];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                throw new \Exception("必填参数 {$field} 不能为空");
            }
        }

        // 验证经纬度格式
        if (!is_numeric($data['recipientLng']) || !is_numeric($data['recipientLat']) ||
            !is_numeric($data['senderLng']) || !is_numeric($data['senderLat'])) {
            throw new \Exception("经纬度参数格式错误");
        }

        // 验证重量（最大50kg）
        if (!is_numeric($data['totalWeight']) || $data['totalWeight'] <= 0 || $data['totalWeight'] > 50000) {
            throw new \Exception("物品重量参数错误，重量应在1-50000克之间");
        }

        // 验证预约单时间参数
        if ($data['prebook'] == 1) {
            if (empty($data['expectedDeliveryTime'])) {
                throw new \Exception("预约单必须提供期望送达时间");
            }

            // 验证预约时间不能是过去时间
            if ($data['expectedDeliveryTime'] <= time()) {
                throw new \Exception("预约送达时间不能是过去时间");
            }

            // 验证预约时间不能超过7天
            if ($data['expectedDeliveryTime'] > time() + 7 * 24 * 3600) {
                throw new \Exception("预约送达时间不能超过7天");
            }
        }

        // 验证货品价值（如果保价）
        if (isset($data['insuredMark']) && $data['insuredMark'] == 1) {
            if (!isset($data['totalValue']) || !is_numeric($data['totalValue']) || $data['totalValue'] <= 0) {
                throw new \Exception("保价订单必须提供有效的货品价值");
            }
            if ($data['totalValue'] > 10000) {
                throw new \Exception("货品价值不能超过10000元");
            }
        }
    }

    /**
     * 检查运力覆盖
     *
     * @param int $lng 经度（火星坐标 * 10^6）
     * @param int $lat 纬度（火星坐标 * 10^6）
     * @return Site|null
     */
    private function checkCapacity(int $lng, int $lat): ?Site
    {
        // 将火星坐标转换为标准坐标
        $longitude = $lng / 1000000;
        $latitude = $lat / 1000000;

        $commonService = app(CommonService::class);
        return $commonService->getPointSite(['lng' => $longitude, 'lat' => $latitude]);
    }

    /**
     * 计算两点间距离
     *
     * @param int $fromLng 起点经度（火星坐标 * 10^6）
     * @param int $fromLat 起点纬度（火星坐标 * 10^6）
     * @param int $toLng 终点经度（火星坐标 * 10^6）
     * @param int $toLat 终点纬度（火星坐标 * 10^6）
     * @return int 距离（米）
     */
    private function calculateDistance(int $fromLng, int $fromLat, int $toLng, int $toLat): int
    {
        // 将火星坐标转换为标准坐标
        $fromLongitude = $fromLng / 1000000;
        $fromLatitude = $fromLat / 1000000;
        $toLongitude = $toLng / 1000000;
        $toLatitude = $toLat / 1000000;

        // 验证坐标范围
        if ($fromLatitude < -90 || $fromLatitude > 90 || $toLatitude < -90 || $toLatitude > 90 ||
            $fromLongitude < -180 || $fromLongitude > 180 || $toLongitude < -180 || $toLongitude > 180) {
            throw new \Exception("经纬度坐标超出有效范围");
        }

        try {
            $mapService = app(MapService::class);
            $result = $mapService->distanceMatrix(
                "{$fromLongitude},{$fromLatitude}",
                "{$toLongitude},{$toLatitude}"
            );

            if ($result['status'] == 0 && isset($result['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = intval($result['result']['rows'][0]['elements'][0]['distance']);

                // 验证距离合理性（最大100公里）
                if ($distance > 100000) {
                    Log::channel('haibo')->warning('计算距离异常，距离过大', [
                        'distance' => $distance,
                        'from' => "{$fromLongitude},{$fromLatitude}",
                        'to' => "{$toLongitude},{$toLatitude}"
                    ]);
                    return $this->calculateStraightDistance($fromLatitude, $fromLongitude, $toLatitude, $toLongitude);
                }

                return $distance;
            }
        } catch (\Exception $e) {
            Log::channel('haibo')->warning('地图API调用失败，使用直线距离', [
                'error' => $e->getMessage(),
                'from' => "{$fromLongitude},{$fromLatitude}",
                'to' => "{$toLongitude},{$toLatitude}"
            ]);
        }

        // 如果API调用失败，使用直线距离估算
        return $this->calculateStraightDistance($fromLatitude, $fromLongitude, $toLatitude, $toLongitude);
    }

    /**
     * 计算直线距离（备用方法）
     *
     * @param float $lat1 起点纬度
     * @param float $lng1 起点经度
     * @param float $lat2 终点纬度
     * @param float $lng2 终点经度
     * @return int 距离（米）
     */
    private function calculateStraightDistance(float $lat1, float $lng1, float $lat2, float $lng2): int
    {
        $earthRadius = 6371000; // 地球半径（米）

        $lat1Rad = deg2rad($lat1);
        $lat2Rad = deg2rad($lat2);
        $deltaLatRad = deg2rad($lat2 - $lat1);
        $deltaLngRad = deg2rad($lng2 - $lng1);

        $a = sin($deltaLatRad / 2) * sin($deltaLatRad / 2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($deltaLngRad / 2) * sin($deltaLngRad / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return intval($earthRadius * $c);
    }

    /**
     * 计算配送费用
     *
     * @param array $data 询价数据
     * @param Site $site 站点信息
     * @param int $distance 距离（米）
     * @return array 费用信息
     */
    private function calculateDeliveryFee(array $data, Site $site, int $distance): array
    {
        // 获取重量（克转公斤）
        $weight = $data['totalWeight'] / 1000;

        // 确定预约时间
        $appointmentTime = $data['prebook'] == 1 && !empty($data['expectedDeliveryTime'])
            ? date('Y-m-d H:i:s', $data['expectedDeliveryTime'])
            : Carbon::now()->format('Y-m-d H:i:s');

        // 使用现有的定价计算逻辑
        $pricingResult = Pricing::calculateFreight(
            Pricing::TYPE_USER,
            $site->id,
            $distance,
            $appointmentTime,
            false, // 非专人配送
            false, // 非恶劣天气
            $weight
        );

        // 计算基础配送费（分转元）
        $deliveryFee = fentoyuan($pricingResult['freight'] + $pricingResult['distance_price'] +
                                $pricingResult['time_price'] + $pricingResult['weather_price'] +
                                $pricingResult['weight_price']);

        // 计算保价费
        $insuredFee = 0.00;
        if (isset($data['insuredMark']) && $data['insuredMark'] == 1 && isset($data['totalValue'])) {
            $insuredFee = $this->calculateInsuredFee($data['totalValue']);
        }

        // 计算优惠金额（暂时为0，后续可扩展）
        $discountFee = 0.00;

        // 计算实际支付金额
        $actualFee = $deliveryFee + $insuredFee - $discountFee;

        return [
            'deliveryFee' => floatval($deliveryFee),
            'insuredFee' => $insuredFee,
            'discountFee' => $discountFee,
            'actualFee' => floatval($actualFee)
        ];
    }

    /**
     * 计算保价费
     *
     * @param float $totalValue 货品价值（元）
     * @return float 保价费（元）
     */
    private function calculateInsuredFee(float $totalValue): float
    {
        // 保价费率：货品价值的0.5%，最低1元，最高100元
        $insuredFee = $totalValue * 0.005;
        return min(max($insuredFee, 1.00), 100.00);
    }

    /**
     * 计算预计送达时间
     *
     * @param array $data 询价数据
     * @param int $distance 距离（米）
     * @return int 预计送达时间戳
     */
    private function calculateDeliveryTime(array $data, int $distance): int
    {
        // 如果是预约单，使用期望送达时间
        if ($data['prebook'] == 1 && !empty($data['expectedDeliveryTime'])) {
            return intval($data['expectedDeliveryTime']);
        }

        // 即时单配送时间计算
        // 基础配送时间：30分钟
        $baseTime = 30 * 60;

        // 距离时间：每公里增加3分钟（优化后更合理）
        $distanceKm = $distance / 1000;
        $distanceTime = intval($distanceKm) * 3 * 60;

        // 重量时间：超过5kg每kg增加1分钟
        $weightTime = 0;
        if (isset($data['totalWeight']) && $data['totalWeight'] > 5000) {
            $extraWeight = ($data['totalWeight'] - 5000) / 1000; // 超出重量（kg）
            $weightTime = intval($extraWeight) * 60; // 每kg增加1分钟
        }

        // 高峰时段增加时间（11:00-13:00, 17:00-19:00）
        $peakTime = 0;
        $currentHour = intval(date('H'));
        if (($currentHour >= 11 && $currentHour < 13) || ($currentHour >= 17 && $currentHour < 19)) {
            $peakTime = 10 * 60; // 高峰时段增加10分钟
        }

        // 总配送时间（最少30分钟，最多120分钟）
        $totalTime = $baseTime + $distanceTime + $weightTime + $peakTime;
        $totalTime = max(30 * 60, min(120 * 60, $totalTime));

        // 即时单：当前时间 + 配送时间
        return time() + $totalTime;
    }

    /**
     * 根据配送商ID查找用户ID
     *
     * @param string $carrierMerchantId
     * @return int|null
     */
    private function findUserByCarrierMerchantId(string $carrierMerchantId): ?int
    {
        $merchant = null;
        if (strpos($carrierMerchantId, 'HB_') === 0) {
            $merchantId = substr($carrierMerchantId, 3);
            $merchant = Merchant::where('merchant_type', 'haibo')
                ->where('id', $merchantId)
                ->first();
        }

        return $merchant ? $merchant->user_id : null;
    }

    /**
     * 格式化已有订单的返回结果
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    private function formatExistingOrderResult(O2oErrandOrder $order): array
    {
        // 计算实际支付金额（与 performNewOrderValuating 逻辑保持一致）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        return [
            'predictDeliveryTime' => $order->estimated_delivery_time->timestamp,
            'actualFee' => $actualFee,
            'deliveryFee' => $actualFee,
            'deliveryDistance' => $order->distance,
            'discountFee' => 0.0,
            'insuredFee' => 0.0,
        ];
    }

    /**
     * 执行新订单询价
     *
     * @param int $userId
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function performNewOrderValuating(int $userId, array $data): array
    {
        $service = new O2oErrandOrderService();

        // 准备预约时间
        $appointmentTime = "NOW";
        if ($data['prebook'] == 1) {
            $expectedTime = 0;
            if (isset($data['expectedDeliveryTime']) && $data['expectedDeliveryTime'] > 0) {
                $expectedTime = $data['expectedDeliveryTime'];
            }else if (isset($data['expectedLeftDeliveryTime']) && $data['expectedLeftDeliveryTime'] > 0) { // 如果预约时间是时间段，则取左区间
                $expectedTime = $data["expectedLeftDeliveryTime"];
            }
            $appointmentTime = date("Y-m-d H:i:s", $expectedTime - 30 * 60) . "|" . date("Y-m-d H:i:s", $expectedTime);
        }

        // 转换经纬度格式（海博传入的是整数，需要转换为小数）
        $senderLng = $data['senderLng'] / 1000000;
        $senderLat = $data['senderLat'] / 1000000;
        $recipientLng = $data['recipientLng'] / 1000000;
        $recipientLat = $data['recipientLat'] / 1000000;

        // 调用 O2oErrandOrderService 的 preOrder 方法
        $preOrderParams = [
            "type" => 1, // 普通配送
            "coupon_id" => 0,
            "gratuity" => 0, // 海博暂不支持小费
            "appointment_time" => $appointmentTime,
            "goods_info" => [
                "is_protect_price" => isset($data['insuredMark']) && $data['insuredMark'] == 1,
                "price" => isset($data['totalValue']) ? $data['totalValue'] : 0,
                "weight" => $data['totalWeight'], // 海博传入的是克，直接使用
            ],
            "start_point" => [
                "mode" => 1, // 指定地址
                "lng" => $senderLng,
                "lat" => $senderLat,
            ],
            "end_point" => [
                "address_id" => 0, // 海博不使用地址簿
                "lng" => $recipientLng,
                "lat" => $recipientLat,
            ],
        ];

        $res = $service->preOrder($userId, $preOrderParams);

        return [
            'predictDeliveryTime' => strtotime($res["normal"]["estimated_delivery_time"]),
            'actualFee' => $res["normal"]["total_amount"],
            'deliveryFee' => $res["normal"]["total_amount"], // 海博的 deliveryFee 等于 actualFee（不含保价费）
            'deliveryDistance' => $res['distance'],
            'discountFee' => 0.00, // 暂不支持优惠
            'insuredFee' => 0.00, // 保价费在 actualFee 中已包含
        ];
    }
}
